import React, { useState, forwardRef } from 'react';
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { ValidationError } from '@/context/auth/types';
import { cn } from '@/lib/utils';

interface FormFieldProps {
  id: string;
  label: string;
  type?: 'text' | 'email' | 'password';
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  placeholder?: string;
  error?: ValidationError | null;
  isValid?: boolean;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  icon?: React.ReactNode;
  showPasswordToggle?: boolean;
  autoComplete?: string;
  maxLength?: number;
  'data-testid'?: string;
}

const FormField = forwardRef<HTMLInputElement, FormFieldProps>(
  (
    {
      id,
      label,
      type = 'text',
      value,
      onChange,
      onBlur,
      onFocus,
      placeholder,
      error,
      isValid,
      required = false,
      disabled = false,
      className,
      icon,
      showPasswordToggle = false,
      autoComplete,
      maxLength,
      'data-testid': testId,
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const [isFocused, setIsFocused] = useState(false);

    const inputType = type === 'password' && showPassword ? 'text' : type;
    const hasError = error !== null && error !== undefined;
    const showValidIcon = isValid && value.length > 0 && !hasError;

    const handleFocus = () => {
      setIsFocused(true);
      onFocus?.();
    };

    const handleBlur = () => {
      setIsFocused(false);
      onBlur?.();
    };

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    return (
      <div className={cn('space-y-2', className)}>
        {/* Label */}
        <Label
          htmlFor={id}
          className={cn(
            'text-base font-semibold transition-colors duration-200',
            hasError ? 'text-red-600' : 'text-gray-700',
            required && "after:content-['*'] after:ml-1 after:text-red-500"
          )}
        >
          {label}
        </Label>

        {/* Input container */}
        <div className="relative">
          {/* Left icon */}
          {icon && (
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
              <div
                className={cn(
                  'transition-colors duration-200',
                  hasError
                    ? 'text-red-400'
                    : isFocused
                    ? 'text-red-500'
                    : 'text-gray-400'
                )}
              >
                {icon}
              </div>
            </div>
          )}

          {/* Input field */}
          <Input
            ref={ref}
            id={id}
            type={inputType}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            autoComplete={autoComplete}
            maxLength={maxLength}
            data-testid={testId}
            className={cn(
              // Base styles
              'h-14 text-lg bg-white border-2 rounded-xl transition-all duration-200',
              'focus:ring-2 focus:ring-red-500/20 focus:outline-none',
              
              // Padding adjustments for icons
              icon ? 'pl-12' : 'pl-4',
              (showPasswordToggle || showValidIcon) ? 'pr-12' : 'pr-4',
              
              // State-based styles
              hasError
                ? 'border-red-500 focus:border-red-500'
                : showValidIcon
                ? 'border-green-500 focus:border-green-500'
                : 'border-gray-200 focus:border-red-500',
              
              // Disabled state
              disabled && 'opacity-50 cursor-not-allowed',
              
              // Touch target optimization for mobile
              'min-h-[44px] touch-manipulation'
            )}
          />

          {/* Right side icons */}
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
            {/* Validation icon */}
            {showValidIcon && (
              <CheckCircle className="h-5 w-5 text-green-500" />
            )}

            {/* Password toggle */}
            {showPasswordToggle && type === 'password' && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-transparent"
                onClick={togglePasswordVisibility}
                disabled={disabled}
                tabIndex={-1}
                data-testid={`${testId}-password-toggle`}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Error message */}
        {hasError && (
          <div
            className="flex items-center space-x-2 text-red-600 animate-fade-in"
            role="alert"
            aria-live="polite"
          >
            <AlertCircle className="h-4 w-4 flex-shrink-0" />
            <span className="text-sm font-medium">{error.message}</span>
          </div>
        )}

        {/* Character count (if maxLength is specified) */}
        {maxLength && (
          <div className="flex justify-end">
            <span
              className={cn(
                'text-xs',
                value.length > maxLength * 0.9
                  ? 'text-orange-500'
                  : 'text-gray-400'
              )}
            >
              {value.length}/{maxLength}
            </span>
          </div>
        )}
      </div>
    );
  }
);

FormField.displayName = 'FormField';

export default FormField;
