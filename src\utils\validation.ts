import { ValidationError, PasswordValidation, PasswordStrength } from '@/context/auth/types';

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Password requirements
const PASSWORD_MIN_LENGTH = 8;
const SPECIAL_CHARS_REGEX = /[!@#$%^&*(),.?":{}|<>]/;
const UPPERCASE_REGEX = /[A-Z]/;
const LOWERCASE_REGEX = /[a-z]/;
const NUMBERS_REGEX = /[0-9]/;

/**
 * Validates email format
 */
export const validateEmail = (email: string): ValidationError | null => {
  if (!email.trim()) {
    return { field: 'email', message: 'E-Mail-Adresse ist erforderlich' };
  }
  
  if (!EMAIL_REGEX.test(email)) {
    return { field: 'email', message: 'Bitte geben Sie eine gültige E-Mail-Adresse ein' };
  }
  
  return null;
};

/**
 * Validates name format
 */
export const validateName = (name: string): ValidationError | null => {
  if (!name.trim()) {
    return { field: 'name', message: 'Name ist erforderlich' };
  }
  
  if (name.trim().length < 2) {
    return { field: 'name', message: 'Name muss mindestens 2 Zeichen lang sein' };
  }
  
  if (name.trim().length > 50) {
    return { field: 'name', message: 'Name darf maximal 50 Zeichen lang sein' };
  }
  
  return null;
};

/**
 * Validates password and returns detailed analysis
 */
export const validatePassword = (password: string): PasswordValidation => {
  const requirements = {
    minLength: password.length >= PASSWORD_MIN_LENGTH,
    hasUppercase: UPPERCASE_REGEX.test(password),
    hasLowercase: LOWERCASE_REGEX.test(password),
    hasNumbers: NUMBERS_REGEX.test(password),
    hasSpecialChars: SPECIAL_CHARS_REGEX.test(password),
  };
  
  // Calculate score based on requirements met
  const score = Object.values(requirements).filter(Boolean).length;
  
  // Determine strength
  let strength: PasswordStrength;
  if (score <= 2) {
    strength = 'weak';
  } else if (score === 3) {
    strength = 'fair';
  } else if (score === 4) {
    strength = 'good';
  } else {
    strength = 'strong';
  }
  
  // Generate suggestions
  const suggestions: string[] = [];
  if (!requirements.minLength) {
    suggestions.push(`Mindestens ${PASSWORD_MIN_LENGTH} Zeichen verwenden`);
  }
  if (!requirements.hasUppercase) {
    suggestions.push('Großbuchstaben hinzufügen');
  }
  if (!requirements.hasLowercase) {
    suggestions.push('Kleinbuchstaben hinzufügen');
  }
  if (!requirements.hasNumbers) {
    suggestions.push('Zahlen hinzufügen');
  }
  if (!requirements.hasSpecialChars) {
    suggestions.push('Sonderzeichen hinzufügen (!@#$%^&*)');
  }
  
  return {
    strength,
    score,
    requirements,
    suggestions,
  };
};

/**
 * Validates password confirmation
 */
export const validatePasswordConfirmation = (
  password: string,
  confirmPassword: string
): ValidationError | null => {
  if (!confirmPassword.trim()) {
    return { field: 'confirmPassword', message: 'Passwort-Bestätigung ist erforderlich' };
  }
  
  if (password !== confirmPassword) {
    return { field: 'confirmPassword', message: 'Passwörter stimmen nicht überein' };
  }
  
  return null;
};

/**
 * Validates login credentials
 */
export const validateLoginCredentials = (
  emailOrName: string,
  password: string
): ValidationError[] => {
  const errors: ValidationError[] = [];
  
  if (!emailOrName.trim()) {
    errors.push({ field: 'emailOrName', message: 'E-Mail oder Name ist erforderlich' });
  }
  
  if (!password.trim()) {
    errors.push({ field: 'password', message: 'Passwort ist erforderlich' });
  }
  
  return errors;
};

/**
 * Validates registration form data
 */
export const validateRegistrationData = (
  name: string,
  email: string,
  password: string,
  confirmPassword: string
): ValidationError[] => {
  const errors: ValidationError[] = [];
  
  // Validate name
  const nameError = validateName(name);
  if (nameError) errors.push(nameError);
  
  // Validate email
  const emailError = validateEmail(email);
  if (emailError) errors.push(emailError);
  
  // Validate password
  const passwordValidation = validatePassword(password);
  if (passwordValidation.strength === 'weak') {
    errors.push({ 
      field: 'password', 
      message: 'Passwort ist zu schwach. Bitte folgen Sie den Empfehlungen.' 
    });
  }
  
  // Validate password confirmation
  const confirmError = validatePasswordConfirmation(password, confirmPassword);
  if (confirmError) errors.push(confirmError);
  
  return errors;
};

/**
 * Debounced validation function
 */
export const createDebouncedValidator = <T extends any[]>(
  validator: (...args: T) => ValidationError | ValidationError[] | null,
  delay: number = 300
) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: T): Promise<ValidationError | ValidationError[] | null> => {
    return new Promise((resolve) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        resolve(validator(...args));
      }, delay);
    });
  };
};

/**
 * Real-time form validation helper
 */
export const createFormValidator = () => {
  const touched: Record<string, boolean> = {};
  const errors: Record<string, ValidationError> = {};
  
  const setTouched = (field: string) => {
    touched[field] = true;
  };
  
  const setError = (error: ValidationError) => {
    errors[error.field] = error;
  };
  
  const clearError = (field: string) => {
    delete errors[field];
  };
  
  const getFieldError = (field: string): ValidationError | null => {
    return touched[field] ? errors[field] || null : null;
  };
  
  const isFieldValid = (field: string): boolean => {
    return !touched[field] || !errors[field];
  };
  
  const isFormValid = (): boolean => {
    return Object.keys(errors).length === 0;
  };
  
  const reset = () => {
    Object.keys(touched).forEach(key => delete touched[key]);
    Object.keys(errors).forEach(key => delete errors[key]);
  };
  
  return {
    setTouched,
    setError,
    clearError,
    getFieldError,
    isFieldValid,
    isFormValid,
    reset,
    touched,
    errors,
  };
};
