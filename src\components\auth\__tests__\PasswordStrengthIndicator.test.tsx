import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import PasswordStrengthIndicator from '../PasswordStrengthIndicator';
import { PasswordValidation } from '@/context/auth/types';

describe('PasswordStrengthIndicator', () => {
  const createMockValidation = (overrides: Partial<PasswordValidation> = {}): PasswordValidation => ({
    strength: 'weak',
    score: 1,
    requirements: {
      minLength: false,
      hasUppercase: false,
      hasLowercase: true,
      hasNumbers: false,
      hasSpecialChars: false,
    },
    suggestions: ['Mindestens 8 Zeichen verwenden', 'Großbuchstaben hinzufügen'],
    ...overrides,
  });

  it('should render password strength indicator', () => {
    const validation = createMockValidation();
    
    render(<PasswordStrengthIndicator validation={validation} />);
    
    expect(screen.getByText('Passwort-Stärke')).toBeInTheDocument();
    expect(screen.getByText('Schwach')).toBeInTheDocument();
  });

  it('should display correct strength levels', () => {
    const strengths = [
      { strength: 'weak' as const, label: 'Schwach' },
      { strength: 'fair' as const, label: 'Ausreichend' },
      { strength: 'good' as const, label: 'Gut' },
      { strength: 'strong' as const, label: 'Stark' },
    ];

    strengths.forEach(({ strength, label }) => {
      const validation = createMockValidation({ strength });
      const { rerender } = render(<PasswordStrengthIndicator validation={validation} />);
      
      expect(screen.getByText(label)).toBeInTheDocument();
      
      rerender(<div />); // Clear for next iteration
    });
  });

  it('should show progress bar with correct width', () => {
    const validation = createMockValidation({ score: 3 });

    const { container } = render(<PasswordStrengthIndicator validation={validation} />);

    // Progress should be 60% (3/5 * 100)
    const progressBar = container.querySelector('[style*="width: 60%"]');
    expect(progressBar).toBeInTheDocument();
  });

  it('should display requirements checklist', () => {
    const validation = createMockValidation({
      requirements: {
        minLength: true,
        hasUppercase: false,
        hasLowercase: true,
        hasNumbers: false,
        hasSpecialChars: false,
      }
    });
    
    render(<PasswordStrengthIndicator validation={validation} />);
    
    expect(screen.getByText('Anforderungen:')).toBeInTheDocument();
    expect(screen.getByText('Mindestens 8 Zeichen')).toBeInTheDocument();
    expect(screen.getByText('Großbuchstaben (A-Z)')).toBeInTheDocument();
    expect(screen.getByText('Kleinbuchstaben (a-z)')).toBeInTheDocument();
    expect(screen.getByText('Zahlen (0-9)')).toBeInTheDocument();
    expect(screen.getByText('Sonderzeichen (!@#$%^&*)')).toBeInTheDocument();
  });

  it('should show check icons for met requirements', () => {
    const validation = createMockValidation({
      requirements: {
        minLength: true,
        hasUppercase: true,
        hasLowercase: true,
        hasNumbers: false,
        hasSpecialChars: false,
      }
    });

    const { container } = render(<PasswordStrengthIndicator validation={validation} />);

    // Should have check icons for met requirements (green backgrounds)
    const checkIcons = container.querySelectorAll('.bg-green-500');
    expect(checkIcons.length).toBe(3); // 3 requirements met
  });

  it('should display suggestions when provided', () => {
    const validation = createMockValidation({
      suggestions: [
        'Großbuchstaben hinzufügen',
        'Zahlen hinzufügen',
        'Sonderzeichen hinzufügen (!@#$%^&*)'
      ]
    });
    
    render(<PasswordStrengthIndicator validation={validation} />);
    
    expect(screen.getByText('Verbesserungen:')).toBeInTheDocument();
    expect(screen.getByText('Großbuchstaben hinzufügen')).toBeInTheDocument();
    expect(screen.getByText('Zahlen hinzufügen')).toBeInTheDocument();
    expect(screen.getByText('Sonderzeichen hinzufügen (!@#$%^&*)')).toBeInTheDocument();
  });

  it('should not show suggestions section when no suggestions', () => {
    const validation = createMockValidation({
      suggestions: []
    });
    
    render(<PasswordStrengthIndicator validation={validation} />);
    
    expect(screen.queryByText('Verbesserungen:')).not.toBeInTheDocument();
  });

  it('should apply custom className', () => {
    const validation = createMockValidation();
    
    render(
      <PasswordStrengthIndicator 
        validation={validation} 
        className="custom-class" 
      />
    );
    
    const container = screen.getByText('Passwort-Stärke').closest('.custom-class');
    expect(container).toBeInTheDocument();
  });

  it('should use correct colors for different strength levels', () => {
    const strengthColors = [
      { strength: 'weak' as const, colorClass: 'bg-red-500' },
      { strength: 'fair' as const, colorClass: 'bg-orange-500' },
      { strength: 'good' as const, colorClass: 'bg-yellow-500' },
      { strength: 'strong' as const, colorClass: 'bg-green-500' },
    ];

    strengthColors.forEach(({ strength, colorClass }) => {
      const validation = createMockValidation({ strength });
      const { container, rerender } = render(
        <PasswordStrengthIndicator validation={validation} />
      );
      
      const progressBar = container.querySelector(`.${colorClass}`);
      expect(progressBar).toBeInTheDocument();
      
      rerender(<div />); // Clear for next iteration
    });
  });

  it('should handle zero score correctly', () => {
    const validation = createMockValidation({
      score: 0,
      strength: 'weak',
      requirements: {
        minLength: false,
        hasUppercase: false,
        hasLowercase: false,
        hasNumbers: false,
        hasSpecialChars: false,
      }
    });

    const { container } = render(<PasswordStrengthIndicator validation={validation} />);

    // Progress should be 0%
    const progressBar = container.querySelector('[style*="width: 0%"]');
    expect(progressBar).toBeInTheDocument();
  });

  it('should handle maximum score correctly', () => {
    const validation = createMockValidation({
      score: 5,
      strength: 'strong',
      requirements: {
        minLength: true,
        hasUppercase: true,
        hasLowercase: true,
        hasNumbers: true,
        hasSpecialChars: true,
      },
      suggestions: []
    });

    const { container } = render(<PasswordStrengthIndicator validation={validation} />);

    // Progress should be 100%
    const progressBar = container.querySelector('[style*="width: 100%"]');
    expect(progressBar).toBeInTheDocument();

    // Should show "Stark"
    expect(screen.getByText('Stark')).toBeInTheDocument();

    // Should not show suggestions section
    expect(screen.queryByText('Verbesserungen:')).not.toBeInTheDocument();
  });
});
