
import { User as AppUser, UserRole } from "../../types";

// Enhanced auth specific types
export interface AuthContextType {
  user: AppUser | null;
  login: (emailOrName: string, password: string, rememberMe?: boolean) => Promise<boolean>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  isLoading: boolean;
  error: AuthError | null;
  users: AppUser[]; // For admin management
  updateUser: (user: AppUser) => void;
  createUser: (user: Omit<AppUser, "id">) => void;
  loginWithGoogle: () => Promise<void>;
  clearError: () => void;
  isAuthenticated: boolean;
}

// Registration data interface
export interface RegisterData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// Enhanced error handling
export interface AuthError {
  type: 'validation' | 'authentication' | 'network' | 'server';
  message: string;
  field?: string;
  code?: string;
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormValidationState {
  isValid: boolean;
  errors: ValidationError[];
  touched: Record<string, boolean>;
}

// Password strength types
export type PasswordStrength = 'weak' | 'fair' | 'good' | 'strong';

export interface PasswordValidation {
  strength: PasswordStrength;
  score: number;
  requirements: {
    minLength: boolean;
    hasUppercase: boolean;
    hasLowercase: boolean;
    hasNumbers: boolean;
    hasSpecialChars: boolean;
  };
  suggestions: string[];
}

// Login form state
export interface LoginFormState {
  emailOrName: string;
  password: string;
  rememberMe: boolean;
  isSubmitting: boolean;
}

// Registration form state
export interface RegisterFormState {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  isSubmitting: boolean;
  currentStep: number;
  maxSteps: number;
}
