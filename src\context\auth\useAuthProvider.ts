import { useState, useEffect, useCallback } from "react";
import { User } from "../../types";
import { mockUsers } from "./mockUsers";
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'sonner';
import { AuthError, RegisterData } from "./types";
import { validateRegistrationData, validateLoginCredentials } from "../../utils/validation";

export const useAuthProvider = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<AuthError | null>(null);
  const [users, setUsers] = useState<User[]>([]);

  // Helper function to create auth errors
  const createAuthError = (
    type: AuthError['type'],
    message: string,
    field?: string,
    code?: string
  ): AuthError => ({
    type,
    message,
    field,
    code,
  });

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Check if user is authenticated
  const isAuthenticated = user !== null;

  // Load users from localStorage on initial load
  useEffect(() => {
    console.log("Initial load, checking for stored users and current user...");

    // First load the mock users to ensure we always have these accounts
    setUsers(mockUsers);
    localStorage.setItem("users", JSON.stringify(mockUsers));

    // Check for remembered user session
    const storedUser = localStorage.getItem("currentUser");
    const rememberMe = localStorage.getItem("rememberMe") === "true";
    const sessionExpiry = localStorage.getItem("sessionExpiry");

    if (storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);

        // Check if session is still valid for "remember me" users
        if (rememberMe) {
          setUser(parsedUser);
        } else if (sessionExpiry) {
          const expiryTime = new Date(sessionExpiry);
          if (new Date() < expiryTime) {
            setUser(parsedUser);
          } else {
            // Session expired, clear storage
            localStorage.removeItem("currentUser");
            localStorage.removeItem("sessionExpiry");
            localStorage.removeItem("rememberMe");
          }
        }
      } catch (err) {
        console.error("Error parsing stored user:", err);
        localStorage.removeItem("currentUser");
        localStorage.removeItem("sessionExpiry");
        localStorage.removeItem("rememberMe");
      }
    }

    setIsLoading(false);
  }, []);

  const login = async (emailOrName: string, password: string, rememberMe: boolean = false) => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate input
      const validationErrors = validateLoginCredentials(emailOrName, password);
      if (validationErrors.length > 0) {
        const firstError = validationErrors[0];
        setError(createAuthError('validation', firstError.message, firstError.field));
        return false;
      }

      // Simulate optimized API call delay (reduced from 500ms to 200ms for better UX)
      await new Promise((resolve) => setTimeout(resolve, 200));

      console.log("Attempting to log in with:", emailOrName, "password length:", password.length);
      console.log("Available users:", users.map(u => ({ email: u.email, name: u.name })));

      // Find user with matching email/name and password
      const foundUser = users.find(
        (u) => (u.email === emailOrName || u.name === emailOrName) && u.password === password
      );

      if (foundUser) {
        console.log("User found:", foundUser.name, foundUser.role);
        const { password: _, ...userWithoutPassword } = foundUser;

        // Set user state
        setUser(userWithoutPassword as User);

        // Handle session persistence
        localStorage.setItem("currentUser", JSON.stringify(userWithoutPassword));
        localStorage.setItem("rememberMe", rememberMe.toString());

        if (rememberMe) {
          // Remember me: session lasts 30 days
          const expiryDate = new Date();
          expiryDate.setDate(expiryDate.getDate() + 30);
          localStorage.setItem("sessionExpiry", expiryDate.toISOString());
        } else {
          // Regular session: lasts 24 hours
          const expiryDate = new Date();
          expiryDate.setHours(expiryDate.getHours() + 24);
          localStorage.setItem("sessionExpiry", expiryDate.toISOString());
        }

        toast.success(`Willkommen zurück, ${foundUser.name}!`);
        return true;
      } else {
        console.log("User not found or password incorrect");
        setError(createAuthError('authentication', 'Ungültige Anmeldeinformationen'));
        toast.error("Anmeldung fehlgeschlagen. Ungültiger Benutzername oder Passwort.");
        return false;
      }
    } catch (err) {
      console.error("Login error:", err);
      setError(createAuthError('network', 'Anmeldefehler. Bitte versuchen Sie es erneut.'));
      toast.error("Ein Fehler ist aufgetreten.");
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterData) => {
    setIsLoading(true);
    setError(null);

    try {
      const { name, email, password, confirmPassword } = userData;

      // Validate registration data
      const validationErrors = validateRegistrationData(name, email, password, confirmPassword);
      if (validationErrors.length > 0) {
        const firstError = validationErrors[0];
        setError(createAuthError('validation', firstError.message, firstError.field));
        return;
      }

      // Simulate optimized API call delay (reduced from 1000ms to 250ms)
      await new Promise((resolve) => setTimeout(resolve, 250));

      // Check if user with this email already exists
      const existingUser = users.find((u) => u.email === email);
      if (existingUser) {
        setError(createAuthError('validation', 'Diese E-Mail-Adresse wird bereits verwendet', 'email'));
        toast.error("Diese E-Mail-Adresse existiert bereits.");
        return;
      }

      // Create new user
      const newUser: User = {
        id: uuidv4(),
        name: name.trim(),
        email: email.toLowerCase().trim(),
        role: "berater", // Default role for new registrations
        teamId: "team1", // Default team
        password, // In a real app, you would hash this
      };

      // Update users list
      const updatedUsers = [...users, newUser];
      setUsers(updatedUsers);
      localStorage.setItem("users", JSON.stringify(updatedUsers));

      // Automatically log in the new user with a 24-hour session
      const { password: _, ...userWithoutPassword } = newUser;
      setUser(userWithoutPassword as User);
      localStorage.setItem("currentUser", JSON.stringify(userWithoutPassword));
      localStorage.setItem("rememberMe", "false");

      const expiryDate = new Date();
      expiryDate.setHours(expiryDate.getHours() + 24);
      localStorage.setItem("sessionExpiry", expiryDate.toISOString());

      toast.success(`Willkommen, ${newUser.name}! Registrierung erfolgreich.`);
    } catch (err) {
      console.error("Registration error:", err);
      setError(createAuthError('server', 'Registrierungsfehler. Bitte versuchen Sie es erneut.'));
      toast.error("Ein Fehler ist aufgetreten.");
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setUser(null);
    setError(null);

    // Clear all session data
    localStorage.removeItem("currentUser");
    localStorage.removeItem("sessionExpiry");
    localStorage.removeItem("rememberMe");

    toast.info("Abmeldung erfolgreich.");

    // Return resolved promise
    return Promise.resolve();
  };

  const updateUser = (updatedUser: User) => {
    setUsers(users.map(u => u.id === updatedUser.id ? updatedUser : u));
    
    // If the current user is being updated, update the local state and storage
    if (user && user.id === updatedUser.id) {
      setUser(updatedUser);
      localStorage.setItem("currentUser", JSON.stringify(updatedUser));
    }
  };

  const createUser = (newUser: Omit<User, "id">) => {
    const userWithId = {
      ...newUser,
      id: uuidv4(),
    };
    
    setUsers([...users, userWithId]);
  };

  // Google login implementation
  const loginWithGoogle = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call delay for Google auth
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // For demonstration purposes, create a Google user
      const googleUser: User = {
        id: uuidv4(),
        name: "Google User",
        email: `user_${Math.floor(Math.random() * 10000)}@gmail.com`,
        role: "berater", // Default role
        teamId: "team1", // Default team
      };

      // Add user to the users array if not already present
      if (!users.some(u => u.email === googleUser.email)) {
        setUsers(prev => [...prev, googleUser]);
      }

      setUser(googleUser);
      localStorage.setItem("currentUser", JSON.stringify(googleUser));
      toast.success(`Willkommen, ${googleUser.name}!`);
    } catch (err) {
      setError("Google-Anmeldung fehlgeschlagen");
      toast.error("Ein Fehler ist bei der Google-Anmeldung aufgetreten.");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    user,
    login,
    register,
    logout,
    isLoading,
    error,
    users,
    updateUser,
    createUser,
    loginWithGoogle,
    clearError,
    isAuthenticated,
  };
};
